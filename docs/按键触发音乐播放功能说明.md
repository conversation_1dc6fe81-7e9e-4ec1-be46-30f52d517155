# 按键触发音乐播放功能说明

## 概述

本文档描述了SmartKid终端设备的按键触发音乐播放功能实现。该功能允许用户通过按下硬件按键来触发音乐播放请求，系统会向服务器发送JSON指令，服务器返回音频流进行播放。

## 功能特性

- ✅ **按键触发**: 支持功能按键1或ADC按键1触发音乐播放
- ✅ **防抖处理**: 1秒防抖时间，避免重复触发
- ✅ **双重检测**: 同时支持GPIO功能按键和ADC按键
- ✅ **JSON通信**: 通过WebSocket发送结构化指令到服务器
- ✅ **音频流播放**: 自动接收和播放服务器返回的音频流
- ✅ **调试友好**: 详细的日志输出，便于调试

## 系统架构

### 整体流程
```
用户按键 → 按键检测 → 防抖处理 → JSON封装 → WebSocket发送 → 服务器处理 → 音频流返回 → 自动播放
```

### 核心组件

1. **按键检测任务** (`SkKeyDetectionTask`)
   - 每100ms检测一次按键状态
   - 支持功能按键和ADC按键检测
   - 实现防抖逻辑

2. **指令发送模块** (`SkSendMusicJsonCommand`)
   - JSON指令生成和发送
   - WebSocket连接状态检查
   - 重试机制

3. **硬件按键支持**
   - GPIO功能按键1 (根据板型不同)
   - ADC按键1 (备选方案)

## 硬件配置

### 支持的板型和按键配置

#### ESP32-S3-BOX3 板型
- **功能按键1**: GPIO_NUM_9
- **功能按键2**: GPIO_NUM_10  
- **功能按键3**: GPIO_NUM_11
- **按键模式**: CONFIG_SK_NORMAL_KEY (GPIO按键)

#### FT-Board 板型
- **功能按键1**: GPIO_NUM_0
- **功能按键2**: GPIO_NUM_11
- **功能按键3**: GPIO_NUM_21
- **按键模式**: CONFIG_SK_NORMAL_KEY (GPIO按键)

#### Outsource 板型
- **按键模式**: CONFIG_SK_ADC_KEY (ADC按键)
- **ADC按键1**: 电压范围 2600-2700mV
- **ADC按键2**: 电压范围 1900-2000mV
- **ADC按键3**: 电压范围 900-1200mV
- **ADC按键4**: 电压范围 600-700mV

## 使用方法

### 基本操作流程

1. **设备准备**
   - 确保设备已连接WiFi
   - 确保WebSocket连接到服务器正常
   - 确保音频服务器运行并有音频文件

2. **触发音乐播放**
   - 按下功能按键1 (GPIO按键模式)
   - 或按下ADC按键1 (ADC按键模式)
   - 系统会自动发送音乐播放指令

3. **观察日志输出**
   ```
   I (12345) SmartKid: Function Key 1 pressed - Triggering music playback
   I (12346) SmartKid: Sending music command: {"type":"command",...}
   I (12347) SmartKid: Music command sent successfully
   ```

### 防抖机制

- **防抖时间**: 1秒
- **作用**: 防止按键抖动导致的重复触发
- **日志提示**: "pressed too soon, ignoring (debounce)"

## 技术实现细节

### 按键检测任务

```c
void SkKeyDetectionTask(void *arg) {
    // 每100ms检测一次按键状态
    // 检测功能按键和ADC按键
    // 实现防抖逻辑
    // 调用音乐播放指令发送函数
}
```

### 关键特性

1. **任务优先级**: 3 (中等优先级)
2. **堆栈大小**: 2048字节
3. **检测频率**: 每100ms
4. **防抖时间**: 1000ms

### JSON指令格式

```json
{
  "type": "command",
  "data": {
    "cmd": "play_music",
    "action": "start",
    "sessionId": "1234567890",
    "timestamp": 1234567890
  }
}
```

## 调试和日志

### 关键日志信息

```
I (12345) SmartKid: Key detection task started - Press Function Key 1 or ADC Key 1 to play music
I (12346) SmartKid: Function Key 1 pressed - Triggering music playback
I (12347) SmartKid: Sending music command: {"type":"command",...}
I (12348) SmartKid: WebSocket connected, sending JSON command
I (12349) SmartKid: Music command sent successfully
```

### 错误处理

- **WebSocket未连接**: "WebSocket not connected, cannot send music command"
- **发送失败**: 自动重试机制，500ms后重试一次
- **按键防抖**: "pressed too soon, ignoring (debounce)"

## 与语音控制的关系

- **保留语音功能**: 语音指令"音乐"仍然有效
- **双重触发**: 按键和语音都可以触发音乐播放
- **相同处理**: 两种方式都调用相同的`SkSendMusicJsonCommand()`函数

## 配置选项

### 编译时配置

- `CONFIG_FUNC_KEY_MODE`: 按键模式选择
  - `CONFIG_SK_NORMAL_KEY`: GPIO按键模式
  - `CONFIG_SK_ADC_KEY`: ADC按键模式

### 运行时参数

- 防抖时间: 可在代码中修改 (当前1000ms)
- 检测频率: 可在代码中修改 (当前100ms)
- 任务优先级: 可在代码中修改 (当前3)

## 故障排除

### 常见问题

1. **按键无响应**
   - 检查硬件连接
   - 确认板型配置正确
   - 查看按键检测任务是否启动

2. **重复触发**
   - 检查防抖时间设置
   - 确认按键硬件质量

3. **音乐不播放**
   - 检查WebSocket连接状态
   - 确认服务器有音频文件
   - 查看服务器日志

### 调试建议

1. **启用详细日志**: 查看按键检测和指令发送的详细日志
2. **测试按键**: 使用万用表测试按键电压变化
3. **网络检查**: 确认WebSocket连接稳定

## 扩展功能

### 可扩展的按键功能

可以通过修改按键检测逻辑添加更多功能：

```c
// 按键2: 暂停/恢复
if (currentKeyState == 2 && ctrl->lastKeyState != 2) {
    // 发送暂停/恢复指令
}

// 按键3: 停止播放
if (currentKeyState == 3 && ctrl->lastKeyState != 3) {
    // 发送停止指令
}
```

### 可配置的防抖时间

```c
#define KEY_DEBOUNCE_TIME_MS 1000  // 可配置的防抖时间
```

## 注意事项

1. **硬件兼容**: 不同板型的按键配置不同，需要确认硬件配置
2. **网络依赖**: 功能依赖WebSocket连接，需要确保网络稳定
3. **服务器要求**: 需要音频服务器支持play_music指令
4. **内存使用**: 按键检测任务占用2KB堆栈空间
5. **实时性**: 100ms检测频率保证了良好的响应性

## 总结

按键触发音乐播放功能提供了一种便捷的调试和测试方式，无需语音即可快速触发音乐播放。该功能具有良好的硬件兼容性、可靠的防抖机制和详细的日志输出，非常适合开发和调试阶段使用。
