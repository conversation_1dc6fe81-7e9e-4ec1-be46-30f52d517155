# 服务器日志增强测试指南

## 更新内容

已对WebSocket音频服务器进行了日志增强，现在可以清晰地看到ESP32音乐播放请求的处理过程。

### 🔧 主要改进

1. **详细的JSON消息日志** - 显示完整的消息内容和结构
2. **音乐指令专门处理** - 特别标识和处理音乐播放请求
3. **音频流发送状态** - 实时显示音频包发送情况
4. **错误诊断信息** - 详细的错误信息和解决建议

## 📋 测试步骤

### 步骤1：准备测试音频文件

```bash
# 创建测试音频文件
python3 create_test_audio.py
```

这会在`audio/`文件夹中创建几个测试WAV文件。

### 步骤2：启动增强版服务器

```bash
# 启动WebSocket音频服务器
python3 websocket_audio_server.py
```

### 步骤3：重新编译ESP32固件

```bash
# 编译并烧录固件（包含notification消息处理修复）
idf.py build
idf.py flash
```

### 步骤4：测试语音功能

1. 说"悟空"唤醒设备
2. 说"音乐"触发播放
3. 观察服务器日志输出

## 📊 预期的服务器日志输出

### 正常流程日志

```
[16:10:30] Client 6 connected: ('192.168.3.45', 62450)
[16:10:30] Sent welcome message to ('192.168.3.45', 62450)

[16:10:45] 📨 收到来自 ('192.168.3.45', 62450) 的JSON消息:
[16:10:45] 📄 消息内容: {
  "type": "command",
  "data": {
    "cmd": "play_music",
    "action": "start",
    "sessionId": "4073",
    "timestamp": 4073
  }
}
[16:10:45] 🏷️  消息类型: command
[16:10:45] 🎵 检测到音乐播放请求!
[16:10:45] 📋 指令详情:
[16:10:45]    - 动作: start
[16:10:45]    - 会话ID: 4073
[16:10:45]    - 时间戳: 4073
[16:10:45] 🎵 处理来自 ('192.168.3.45', 62450) 的音乐播放请求
[16:10:45] ✅ 找到 4 个音频文件
[16:10:45] 🚀 启动音频流播放...
[16:10:45] 📤 发送 music_ready 响应给 ('192.168.3.45', 62450)
[16:10:45] 🎶 音频流开始发送，客户端应该很快收到音频数据
[16:10:45] 🎵 开始向 ('192.168.3.45', 62450) 发送音频流
[16:10:45] ✅ 首个音频包已发送给 ('192.168.3.45', 62450)
[16:10:45] 📊 音频包信息: 序列号=1, 大小=68字节
[16:10:46] 📈 已发送 50 个音频包给 ('192.168.3.45', 62450)
[16:10:47] 📈 已发送 100 个音频包给 ('192.168.3.45', 62450)
```

### 错误情况日志

如果没有音频文件：
```
[16:10:45] 🎵 处理来自 ('192.168.3.45', 62450) 的音乐播放请求
[16:10:45] 📁 没有音频文件，尝试重新加载...
[16:10:45] ❌ 没有可用的音频文件
[16:10:45] 📤 发送 music_error 响应给 ('192.168.3.45', 62450)
[16:10:45] 💡 提示: 请在 audio/ 文件夹中放置 WAV 音频文件
```

## 🔍 ESP32端对应日志

ESP32端应该显示：
```
I (41630) SmartKid: Voice command: Play music
I (41630) SmartKid: Sending music command: {"type":"command","data":{"cmd":"play_music",...}}
I (41640) SmTop: Command 3
I (41640) SmTop: State start 7
E (41640) SmMusic: Start music
I (41720) SkRlink: Processing JSON: {"type": "notification", ...}
I (41720) SkRlink: Received notification message
```

## 🛠️ 故障排除

### 1. 如果服务器没有收到JSON消息

**可能原因**：
- WebSocket连接在发送时断开
- ESP32的JSON发送失败

**解决方法**：
- 检查网络连接稳定性
- 查看ESP32串口日志中的错误信息

### 2. 如果收到消息但没有音频流

**可能原因**：
- audio/文件夹中没有WAV文件
- 音频文件格式不正确

**解决方法**：
```bash
# 创建测试音频文件
python3 create_test_audio.py

# 或者复制现有的WAV文件
cp your_music.wav audio/
```

### 3. 如果音频流发送但ESP32没有播放

**可能原因**：
- ESP32音频解码问题
- 音频硬件问题

**解决方法**：
- 检查ESP32的音频输出硬件
- 查看Opus解码相关日志

## 📈 性能监控

服务器会显示以下性能信息：
- 音频包发送速率（每秒50包）
- 连接状态变化
- 内存使用情况（如果有大量音频文件）

## 🎯 测试成功标志

测试成功的标志：
1. ✅ 服务器收到音乐播放请求JSON
2. ✅ 服务器发送music_ready响应
3. ✅ 服务器开始发送音频包
4. ✅ ESP32播放音频（听到声音）

## 📝 日志分析

通过增强的日志，你可以清楚地看到：
- 消息接收的确切时间
- JSON消息的完整内容
- 音乐指令的详细参数
- 音频流发送的实时状态
- 任何错误的详细信息

这样可以快速定位问题并进行针对性修复。
