# 语音控制音乐播放功能说明文档

## 概述

本文档描述了SmartKid终端设备的语音控制音乐播放功能实现。该功能允许用户通过语音指令"悟空"唤醒设备，然后说"音乐"来触发音乐播放请求，系统会向服务器发送JSON指令，服务器返回音频流进行播放。

## 功能特性

- ✅ **语音唤醒**: 使用"悟空"作为唤醒词
- ✅ **语音指令**: 支持"音乐"指令触发音乐播放
- ✅ **JSON通信**: 通过WebSocket发送结构化指令到服务器
- ✅ **音频流播放**: 自动接收和播放服务器返回的音频流
- ✅ **错误处理**: 支持连接状态检查和错误响应处理

## 系统架构

### 整体流程
```
用户语音 → 语音识别 → 指令处理 → JSON封装 → WebSocket发送 → 服务器处理 → 音频流返回 → 自动播放
```

### 核心组件

1. **语音识别模块** (`sk_sr.c`)
   - 负责唤醒词检测和语音指令识别
   - 使用ESP-IDF语音识别框架

2. **指令处理模块** (`main.c`)
   - `SkMainCmdProc()`: 语音指令回调处理
   - `SkSendMusicJsonCommand()`: JSON指令生成和发送

3. **通信模块** (`sk_websocket.c`, `sk_rlink.c`)
   - WebSocket连接管理
   - JSON消息发送和接收
   - 音频流数据处理

4. **音频播放模块** (`sk_opus_dec.c`)
   - Opus音频解码
   - 音频流播放

## 使用方法

### 基本操作流程

1. **设备准备**
   - 确保设备已连接WiFi
   - 确保WebSocket连接到服务器正常

2. **语音操作**
   ```
   用户: "悟空"          → 系统唤醒，LED指示灯变化
   用户: "音乐"          → 系统发送音乐请求到服务器
   系统: 自动播放返回的音频流
   ```

### 服务器端要求

服务器需要支持以下JSON消息格式：

**客户端发送的音乐请求**:
```json
{
    "type": "command",
    "data": {
        "cmd": "play_music",
        "action": "start",
        "sessionId": "1234567890",
        "timestamp": 1234567890
    }
}
```

**服务器可选响应**:
```json
{
    "type": "command",
    "data": {
        "cmd": "music_ready"
    }
}
```

**服务器错误响应**:
```json
{
    "type": "command", 
    "data": {
        "cmd": "music_error"
    }
}
```

## 技术实现细节

### 代码修改说明

#### 1. main.c 修改
- 添加了 `SkSendMusicJsonCommand()` 函数
- 扩展了 `SkMainCmdProc()` 函数，增加音乐指令处理

#### 2. sk_rlink.c 修改  
- 在 `ProcessJsonCommand()` 函数中添加了音乐相关指令处理
- 支持 `play_music`、`music_ready`、`music_error` 指令

### 关键函数说明

#### `SkSendMusicJsonCommand()`
```c
void SkSendMusicJsonCommand() {
    // 生成JSON格式的音乐播放指令
    // 通过WebSocket发送到服务器
    // 包含会话ID和时间戳
}
```

#### `SkMainCmdProc(int32_t cmd)`
```c
void SkMainCmdProc(int32_t cmd) {
    // 处理语音识别结果
    // 当检测到SPEECH_CMD_EVENT_MUSIC时调用音乐指令发送
    // 保持原有状态机事件处理逻辑
}
```

#### `ProcessJsonCommand()`
```c
static int32_t ProcessJsonCommand(RlinkCtrlInfo *ctrl, cJSON *data) {
    // 处理服务器返回的JSON指令
    // 支持音乐播放相关的响应处理
}
```

## 配置说明

### 语音指令映射
系统使用以下语音指令映射（已在代码中预定义）：
```c
{SPEECH_CMD_EVENT_CHAT, "wu kong"},    // 唤醒词
{SPEECH_CMD_EVENT_MUSIC, "yin yue"},   // 音乐指令
```

### WebSocket配置
- 默认服务器: `************:8768`
- 可通过 `SkWsSetServerIp()` 修改服务器地址

## 调试和日志

### 日志输出
系统会输出以下关键日志信息：

```
I (12345) SmartKid: Voice command: Play music
I (12346) SmartKid: Sending music command: {"type":"command",...}
I (12347) sk_rlink: Command: Play music - requesting music stream from server
I (12348) sk_rlink: Server response: Music stream ready - audio will start playing
```

### 错误处理
- WebSocket连接断开时会记录错误日志
- JSON解析失败时会输出错误信息
- 音频流处理异常时会记录相关错误

## 扩展功能

### 可扩展的语音指令
可以通过修改 `g_skSpeechMap` 数组添加更多语音指令：
```c
{SPEECH_CMD_EVENT_MUSIC_PAUSE, "zan ting yin yue"},  // 暂停音乐
{SPEECH_CMD_EVENT_MUSIC_NEXT, "xia yi shou"},        // 下一首
{SPEECH_CMD_EVENT_MUSIC_PREV, "shang yi shou"},      // 上一首
```

### 可扩展的JSON指令
可以在 `ProcessJsonCommand()` 中添加更多指令处理：
```c
else if (strcmp(cmd->valuestring, "pause_music") == 0) {
    // 处理暂停音乐指令
}
```

## 注意事项

1. **网络连接**: 确保设备WiFi连接稳定，WebSocket连接正常
2. **服务器兼容**: 服务器需要支持指定的JSON消息格式
3. **音频格式**: 服务器返回的音频流需要是Opus编码格式
4. **内存管理**: JSON字符串会自动分配和释放内存
5. **并发处理**: 系统使用FreeRTOS任务和队列机制，支持并发处理

## 故障排除

### 常见问题

1. **语音识别不响应**
   - 检查麦克风是否正常工作
   - 确认语音识别模块是否正确初始化

2. **WebSocket连接失败**
   - 检查网络连接状态
   - 确认服务器地址和端口配置正确

3. **音频播放无声音**
   - 检查音频解码器是否正常工作
   - 确认扬声器硬件连接正常

4. **服务器无响应**
   - 检查服务器是否正常运行
   - 确认JSON消息格式是否正确

### 调试建议

1. 启用详细日志输出
2. 使用网络抓包工具检查WebSocket通信
3. 检查音频数据流的完整性
4. 监控系统内存使用情况

## 版本信息

- **功能版本**: v1.0
- **实现日期**: 2025-08-01
- **兼容性**: ESP-IDF v4.4+
- **测试状态**: 开发完成，待测试验证
