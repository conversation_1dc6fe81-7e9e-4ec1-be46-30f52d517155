# SmartKid终端命令和聊天时序图详细描述

## 🎯 时序图概述

基于对您代码库的深入分析，SmartKid终端系统的命令和聊天功能采用了**事件驱动的状态机架构**，通过精确的时序控制实现智能语音交互。

## 🏗️ 系统参与者

### 核心组件
- **👤 用户**: 语音输入源
- **🎤 麦克风**: 音频采集硬件
- **🧠 语音识别(SR)**: 唤醒词和指令识别
- **🎛️ 状态机(SM)**: 系统核心控制器
- **🌐 WebSocket**: 网络通信层
- **🖥️ 服务器**: 云端AI服务
- **🔊 扬声器**: 音频输出设备

## 📋 详细时序流程分析

### 🎯 阶段1: 唤醒词检测

#### 时序步骤
1. **用户语音输入**: 用户说"悟空"
2. **音频数据采集**: 麦克风持续采集音频流
3. **唤醒词检测**: SR模块检测到唤醒词
4. **状态切换**: 发送`SM_EVENT_SYSTEM_START_VOICE_INPUT`事件
5. **模式激活**: `cmdInputMode = true`，进入指令等待模式

#### 关键代码实现
```c
void SrWackeCheck(SkSrCtrl *ctrl, afe_fetch_result_t* res) {
    if (res->wakeup_state == WAKENET_DETECTED) {
        SkSmPostEvent(SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_START_VOICE_INPUT, 0, 0);
        ESP_LOGI(TAG, "WAKEWORD DETECTED");
        ctrl->cmdInputMode = true;
        ctrl->silenteTime = 0;
        SkSmOnWakeup();
    }
}
```

### 🗣️ 阶段2: 指令识别

#### 时序步骤
1. **指令输入**: 用户说"聊天"、"音乐"等指令
2. **持续音频流**: 麦克风继续采集音频数据
3. **指令识别**: SR模块识别具体指令
4. **事件发送**: 发送`SM_EVENT_CMD + 指令ID`
5. **状态映射**: 根据指令ID映射到对应状态

#### 指令到状态映射
```c
int32_t SmCmdToState(int32_t cmd) {
    switch (cmd) {
        case SPEECH_CMD_EVENT_CHAT:
            return STATE_CALL;      // 聊天状态
        case SPEECH_CMD_EVENT_MUSIC:
            return STATE_MUSIC;     // 音乐状态
        case SPEECH_CMD_EVENT_CONFIG:
            return STATE_CONFIG;    // 配置状态
        default:
            return STATE_IDLE;      // 空闲状态
    }
}
```

### 💬 阶段3A: 聊天模式时序

#### 聊天启动流程
1. **状态切换**: 进入`STATE_CALL`状态
2. **LED指示**: 设置聊天LED状态
3. **音频提示**: 播放"等待"提示音
4. **会话创建**: 生成新的会话ID
5. **模块配置**: 
   - 启用VC处理 (`SkVcProcessEnable(true)`)
   - 禁用SR处理 (`SkSrProcessEnable(false)`)
6. **网络通信**: 通知服务器开始聊天会话

```c
int32_t SmChatStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SmChatCtrl *ctrl = (SmChatCtrl *)info->privateData;
    
    SkRledSetEvent(SK_LED_EVENT_CHAT);          // LED指示
    SkPlayerResume();                           // 恢复播放器
    audioList[0] = AUDIO_IDX_WAIT;
    SkSmPlayLocalNotice(audioList, 1, false);   // 播放等待音
    ctrl->currSessionId = SkSmNewSession(ctrl->handler); // 创建会话
    SkClinkEventNotify(CLINK_EVENT_CALL_AGENT, ctrl->currSessionId, 0, 0);
    SkRecorderResume();                         // 恢复录音
    SkVcProcessEnable(true);                    // 启用语音通信
    SkSrProcessEnable(false);                   // 禁用语音识别
    
    return SK_RET_SUCCESS;
}
```

#### 聊天会话循环
1. **用户说话**: 持续的语音输入
2. **音频处理**: 麦克风采集音频数据
3. **VAD检测**: 检测语音活动状态
4. **Opus编码**: 将音频编码为Opus格式
5. **网络传输**: 通过WebSocket发送到服务器
6. **AI回复**: 服务器返回AI生成的音频回复
7. **音频播放**: 扬声器播放AI回复

#### 聊天退出流程
1. **退出指令**: 用户说"退出"
2. **指令识别**: 识别为`SPEECH_CMD_EVENT_QUIT`
3. **状态清理**: 停止聊天相关处理
4. **模块重置**: 恢复默认配置
5. **返回空闲**: 回到`STATE_IDLE`状态

### 🎵 阶段3B: 音乐模式时序

#### 音乐播放流程
1. **状态切换**: 进入`STATE_MUSIC`状态
2. **指令发送**: 发送音乐播放JSON指令
3. **服务器处理**: 服务器接收`{"cmd":"play_music"}`指令
4. **音频流返回**: 服务器返回音频流数据
5. **实时播放**: 扬声器播放音频流

```c
void SkSendMusicJsonCommand() {
    char jsonCmd[256];
    uint32_t timestamp = SkOsGetTickCnt();
    
    snprintf(jsonCmd, sizeof(jsonCmd),
        "{"
        "\"type\":\"command\","
        "\"data\":{"
        "\"cmd\":\"play_music\","
        "\"action\":\"start\","
        "\"sessionId\":\"%lu\","
        "\"timestamp\":%lu"
        "}}", 
        (unsigned long)timestamp, (unsigned long)timestamp);
    
    if (SkWsIsConnected()) {
        SkWsSendRaw((uint8_t*)jsonCmd, strlen(jsonCmd));
    }
}
```

### 🔄 阶段4: VAD静音检测

#### 静音检测机制
1. **持续监控**: VAD持续检测语音活动
2. **静音计数**: 检测到静音时递增计数器
3. **阈值判断**: 达到32个周期(约640ms)静音
4. **用户确认**: 触发`SkSmOnUserAck()`事件
5. **模式退出**: `cmdInputMode = false`，退出指令模式

```c
void SkSrVadProc(SkSrCtrl *ctrl, afe_fetch_result_t *res) {
    if (!ctrl->cmdInputMode) {
        return;  // 非指令模式直接返回
    }
    
    if ((uint8_t)res->vad_state == (uint8_t)AFE_VAD_SPEECH) {
        ctrl->silenteTime = 0;  // 检测到语音，重置计时
        return;
    }
    
    ctrl->silenteTime++;  // 静音时间递增
    if (ctrl->silenteTime == 32) {  // 640ms静音阈值
        SkSmOnUserAck();  // 触发用户确认事件
        ESP_LOGI(TAG, "Detect silence after wakeup, action user by speech");
    }
}
```

### 📊 阶段5: 系统状态监控

#### 定时器驱动机制
1. **定时器触发**: 每秒触发一次系统定时器
2. **事件发送**: 发送`SM_EVENT_SYSTEM_TICK`事件
3. **状态检查**: 检查各模块运行状态
4. **LED控制**: 更新LED显示状态
5. **按键检测**: 检测功能按键状态

```c
void SmTimerCallback(void* arg) {
    // 每秒发送系统定时事件
    SkSmSendEvent((SkStateHandler)arg, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_TICK, 0, 0);
}
```

## ⏱️ 关键时序参数

### 音频处理时序
- **采样率**: 16kHz
- **帧大小**: 320采样点 (20ms)
- **缓冲延迟**: 最小化，实时处理
- **VAD检测**: 20ms帧级别检测

### 网络通信时序
- **WebSocket**: 实时双向通信
- **心跳间隔**: 60秒超时检测
- **重连机制**: 自动重连，指数退避

### 状态机时序
- **事件处理**: 消息队列，FIFO处理
- **状态切换**: 同步切换，原子操作
- **定时器**: 1秒周期，系统监控

## 🔧 时序优化特点

### 实时性保证
1. **高优先级任务**: 音频处理任务优先级5
2. **CPU绑定**: 音频任务绑定CPU1
3. **最小延迟**: 直接处理，无额外缓冲
4. **硬件加速**: ESP32-S3音频处理优化

### 可靠性保证
1. **事件驱动**: 异步事件处理机制
2. **状态保护**: 状态切换原子性
3. **错误恢复**: 完善的错误处理和恢复
4. **资源管理**: 自动资源清理和释放

### 用户体验优化
1. **智能检测**: VAD静音检测，自动退出
2. **音频提示**: 状态变化音频反馈
3. **LED指示**: 可视化状态指示
4. **快速响应**: 最小化用户等待时间

## 📈 性能监控

### 统计指标
- **语音帧计数**: `vcToCodecCnt`
- **静音帧计数**: `silenceToCodecCnt`
- **AFE处理计数**: `srAfeFeedCnt` / `vcAfeFeedCnt`
- **网络传输统计**: WebSocket连接状态

### 调试支持
- **PCM数据导出**: 多个调试点的音频数据导出
- **状态日志**: 详细的状态切换日志
- **性能统计**: 实时性能监控数据
- **错误追踪**: 完整的错误日志记录

## 🎯 总结

SmartKid终端的命令和聊天时序图展现了一个高度优化的智能语音交互系统：

1. **精确时序**: 20ms帧级别的实时处理
2. **智能检测**: VAD驱动的自动状态管理
3. **模块化设计**: 清晰的模块职责分离
4. **事件驱动**: 高效的异步事件处理
5. **用户友好**: 智能的交互体验设计

该时序设计确保了系统的高实时性、高可靠性和优秀的用户体验。
