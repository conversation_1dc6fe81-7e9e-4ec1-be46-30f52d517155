# SkRecorderTask函数详细分析

## 🎤 函数概述

`SkRecorderTask` 是SmartKid终端系统中的**核心音频录制任务**，负责从硬件麦克风持续采集音频数据，并将其传递给音频处理流水线进行后续的语音识别和编码处理。

## 📋 函数签名和结构

```c
void SkRecorderTask(void *arg)
{
    SkRecorderCtrl *ctrl = &g_recorderCtrl;

    ESP_LOGI(TAG, "Task start");
    while (ctrl->taskFlag) {
        xEventGroupWaitBits(ctrl->eventGroup, RECORDER_EVENT_RUN,
            pdFALSE, pdFALSE, portMAX_DELAY);
        SkBspReadAudio(ctrl->buff, ctrl->bufferSize);
        SkAudioProcessFeed(ctrl->buff, ctrl->samplePerChunk, ctrl->channelNum);                
    }
    ESP_LOGI(TAG, "Task exit");

    vTaskDelete(NULL);
}
```

## 🏗️ 控制结构体分析

### SkRecorderCtrl结构体定义

```c
typedef struct {
    EventGroupHandle_t eventGroup;    // 事件组句柄，用于任务同步
    int32_t bytesPerSample;          // 每个采样点的字节数 (通常为2，即16位)
    int32_t samplePerChunk;          // 每个数据块的采样点数
    int32_t channelNum;              // 声道数 (1=单声道, 2=立体声)
    bool pauseFlag;                  // 暂停标志
    int keyInput;                    // 按键输入
    bool taskFlag;                   // 任务运行标志
    int bufferSize;                  // 缓冲区大小 (字节)
    int16_t *buff;                   // 音频数据缓冲区指针
    uint8_t *debugBuf;               // 调试缓冲区
    int32_t debugBufSize;            // 调试缓冲区大小
    int32_t debugBufPos;             // 调试缓冲区位置
    int32_t audioIndex;              // 音频索引
    int32_t audioPos;                // 音频位置
    int32_t audioCnt;                // 音频计数
    SkAudioSrcItem audioSrcList[MAX_AUDIO_CNT]; // 音频源列表
} SkRecorderCtrl;
```

### 关键参数说明

- **bufferSize**: `samplePerChunk * bytesPerSample * channelNum`
- **典型配置**: 320采样点 × 2字节 × 2声道 = 1280字节
- **采样频率**: 16kHz (每20ms一个数据块)

## 🔧 内部函数详细分析

### 1. xEventGroupWaitBits() - 事件同步机制

```c
xEventGroupWaitBits(ctrl->eventGroup, RECORDER_EVENT_RUN,
    pdFALSE, pdFALSE, portMAX_DELAY);
```

#### 功能作用
- **同步控制**: 等待录音启动事件，实现任务的暂停/恢复控制
- **事件定义**: `#define RECORDER_EVENT_RUN (1 << 0)` (位0)
- **阻塞等待**: `portMAX_DELAY` 表示无限等待，直到事件被设置

#### 参数说明
- `ctrl->eventGroup`: 事件组句柄
- `RECORDER_EVENT_RUN`: 等待的事件位
- `pdFALSE`: 不清除事件位
- `pdFALSE`: 等待任意一个事件位 (不是全部)
- `portMAX_DELAY`: 无限等待

#### 控制机制
```c
// 启动录音
void SkRecorderResume() {
    xEventGroupSetBits(g_recorderCtrl.eventGroup, RECORDER_EVENT_RUN);
}

// 暂停录音
void SkRecorderPause() {
    xEventGroupClearBits(g_recorderCtrl.eventGroup, RECORDER_EVENT_RUN);
}

// 停止录音
void SkRecorderStop() {
    ctrl->taskFlag = false;  // 退出主循环
    xEventGroupSetBits(ctrl->eventGroup, RECORDER_EVENT_RUN); // 唤醒任务
}
```

### 2. SkBspReadAudio() - 硬件音频读取

```c
SkBspReadAudio(ctrl->buff, ctrl->bufferSize);
```

#### 功能作用
- **硬件抽象**: 从底层音频设备读取原始PCM数据
- **阻塞读取**: 等待硬件DMA缓冲区填满指定数据量
- **数据格式**: 16位有符号整数，小端序

#### 底层实现路径
```c
// 板级支持包层
sk_err_t SkBspReadAudio(int16_t *buffer, int bufferLen) {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevReadNoncodec(buffer, bufferLen);    
#else
    return AudioDevRead(buffer, bufferLen);  // 标准音频设备
#endif
}

// 音频设备层
int AudioDevRead(int16_t* dest, int bufSize) {
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;
    if (audioDev->micEnabled) {
        ESP_ERROR_CHECK_WITHOUT_ABORT(esp_codec_dev_read(audioDev->micDev, (void*)dest, bufSize));
    } else {
        memset(dest, 0, bufSize);  // 麦克风禁用时填充静音
    }
    return bufSize;
}
```

#### 数据特征
- **采样率**: 16kHz
- **位深**: 16位
- **声道**: 1或2声道 (根据配置)
- **数据块大小**: 通常320采样点 (20ms)

### 3. SkAudioProcessFeed() - 音频处理流水线

```c
SkAudioProcessFeed(ctrl->buff, ctrl->samplePerChunk, ctrl->channelNum);
```

#### 功能作用
- **数据分发**: 将录制的音频数据分发给多个处理模块
- **流水线入口**: 音频处理流水线的统一入口点
- **多路处理**: 同时支持语音识别、语音编码、调试输出

#### 内部处理流程

```c
void SkAudioProcessFeed(int16_t *buffer, int sampleCnt, int channelNum) {
    SkSrCtrl *ctrl = &g_skSrCtrl;

#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_REC)
    // 调试输出：原始录音数据
    if (channelNum == 2) {
        SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM2, (uint8_t *)buffer, 
                          sampleCnt * sizeof(int16_t) * channelNum);
    } else {
        SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)buffer, 
                          sampleCnt * sizeof(int16_t) * channelNum);
    }
#endif

    // 1. 语音识别数据输入
    SkSrDataIn(buffer, sampleCnt);
    
#ifdef CONFIG_VC_AFE_ENABLED
    // 2. 语音编码AFE处理
    SkVcAfeFeed(ctrl);
#endif
    
    // 3. 语音识别AFE处理
    SkSrAfeFeed(ctrl);

#ifndef CONFIG_VC_AFE_ENABLED
    // 4. 直接编码处理 (无AFE时)
    SkDataToEncode(buffer, sampleCnt);
#endif

#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_REC_CH0)
    // 调试输出：处理后的单声道数据
    SkAudioDumpRecData(buffer, sampleCnt, channelNum);
#endif
}
```

#### 处理模块说明

1. **SkSrDataIn()**: 语音识别数据输入
   - 将音频数据写入SR缓冲区
   - 为语音识别任务提供数据源

2. **SkVcAfeFeed()**: 语音编码AFE处理
   - 音频前端处理 (Audio Front End)
   - 回声消除、噪声抑制、增益控制

3. **SkSrAfeFeed()**: 语音识别AFE处理
   - 唤醒词检测
   - 语音活动检测 (VAD)
   - 语音增强处理

4. **SkDataToEncode()**: 直接编码
   - 将音频数据送入Opus编码器
   - 用于网络传输

## 🔄 任务工作流程

### 1. 任务启动流程

```
系统初始化 → SkAudioStartTasks() → xTaskCreatePinnedToCore() → SkRecorderTask启动
```

### 2. 运行时流程

```
等待RECORDER_EVENT_RUN事件 → 读取音频数据 → 处理音频数据 → 循环
```

### 3. 数据流向

```
麦克风硬件 → I2S/ADC → SkBspReadAudio → SkAudioProcessFeed → 多路分发
                                                                    ↓
                                                            ┌─ 语音识别
                                                            ├─ 语音编码  
                                                            ├─ 网络传输
                                                            └─ 调试输出
```

## ⚡ 性能特征

### 实时性保证
- **任务优先级**: 5 (最高优先级)
- **CPU绑定**: CPU1 (专用音频处理核心)
- **无缓冲延迟**: 直接处理，无额外缓冲
- **20ms延迟**: 每个数据块对应20ms音频

### 内存使用
- **堆栈大小**: 8192字节
- **音频缓冲**: 1280字节 (典型配置)
- **总内存**: 约10KB

## 🎛️ 控制接口

### 外部控制函数

```c
void SkRecorderResume();  // 恢复录音
void SkRecorderPause();   // 暂停录音  
void SkRecorderStop();    // 停止录音
void SkRecorderSetPm(bool flag); // 电源管理
```

### 状态机集成

录音任务与状态机紧密集成，响应系统状态变化：

```c
// 音乐播放状态
case SPEECH_CMD_EVENT_PAUSE:
    SkRecorderPause();  // 暂停录音
    break;
case SPEECH_CMD_EVENT_RESUME:
    SkRecorderResume(); // 恢复录音
    break;
```

## 🔍 调试和监控

### 调试输出
- **原始数据**: CONFIG_PCM_DBG_POS_REC
- **处理后数据**: CONFIG_PCM_DBG_POS_REC_CH0
- **调试链接**: SkDfxLinkSendAudio()

### 性能监控
- **CPU使用率**: 通过FreeRTOS任务统计
- **内存使用**: SK_OS_MODULE_MEM_STAT()
- **数据流量**: 音频数据计数器

## 🎯 总结

`SkRecorderTask` 是整个音频系统的**数据源头**，具有以下特点：

1. **高实时性**: 最高优先级，专用CPU核心
2. **事件驱动**: 通过事件组实现精确的启停控制
3. **流水线架构**: 统一的数据分发机制
4. **硬件抽象**: 良好的硬件抽象层设计
5. **调试友好**: 完善的调试和监控机制

该任务是语音识别、音频编码、网络传输等所有音频功能的基础，其稳定性和实时性直接影响整个系统的音频质量。
