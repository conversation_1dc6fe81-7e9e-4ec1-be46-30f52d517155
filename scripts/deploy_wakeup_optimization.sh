#!/bin/bash

# 小智唤醒响应优化部署脚本
# 用于编译、烧录和测试唤醒优化功能

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖环境..."
    
    # 检查ESP-IDF环境
    if ! command -v idf.py &> /dev/null; then
        print_error "ESP-IDF环境未找到，请先设置ESP-IDF环境"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未找到，请先安装Python3"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 显示优化内容
show_optimizations() {
    print_info "本次优化内容:"
    echo "  🔊 1. 麦克风默认增益: 6 → 9 (提升语音信号强度)"
    echo "  🎛️  2. 唤醒检测阈值: DET_MODE_90 → DET_MODE_80 (提高灵敏度)"
    echo "  📊 3. 添加唤醒统计和调试信息"
    echo "  🎤 4. 添加运行时麦克风音量调整接口"
    echo "  ⚠️  5. 低成功率自动警告提示"
    echo ""
}

# 备份原始文件
backup_files() {
    print_info "备份原始文件..."
    
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份修改的文件
    cp main/config/sk_config.c "$BACKUP_DIR/" 2>/dev/null || true
    cp main/audio/sk_afe.c "$BACKUP_DIR/" 2>/dev/null || true
    cp main/audio/sk_sr.c "$BACKUP_DIR/" 2>/dev/null || true
    cp main/include/sk_config.h "$BACKUP_DIR/" 2>/dev/null || true
    
    print_success "文件已备份到: $BACKUP_DIR"
}

# 编译项目
build_project() {
    print_info "开始编译项目..."
    
    # 清理之前的构建
    print_info "清理之前的构建..."
    idf.py clean
    
    # 编译项目
    print_info "编译项目..."
    if idf.py build; then
        print_success "编译成功!"
    else
        print_error "编译失败!"
        exit 1
    fi
}

# 烧录固件
flash_firmware() {
    print_info "准备烧录固件..."
    
    # 检查串口设备
    if [ -z "$1" ]; then
        print_warning "未指定串口设备，尝试自动检测..."
        
        # 常见的串口设备
        PORTS=("/dev/ttyUSB0" "/dev/ttyUSB1" "/dev/ttyACM0" "/dev/cu.usbserial*")
        
        for port in "${PORTS[@]}"; do
            if ls $port 1> /dev/null 2>&1; then
                SERIAL_PORT=$port
                break
            fi
        done
        
        if [ -z "$SERIAL_PORT" ]; then
            print_error "未找到串口设备，请手动指定"
            echo "使用方法: $0 [串口设备]"
            echo "例如: $0 /dev/ttyUSB0"
            exit 1
        fi
    else
        SERIAL_PORT=$1
    fi
    
    print_info "使用串口设备: $SERIAL_PORT"
    
    # 烧录固件
    print_info "开始烧录固件..."
    if idf.py -p "$SERIAL_PORT" flash; then
        print_success "固件烧录成功!"
    else
        print_error "固件烧录失败!"
        exit 1
    fi
}

# 监控串口输出
monitor_serial() {
    print_info "开始监控串口输出..."
    print_info "按 Ctrl+] 退出监控"
    
    # 等待设备重启
    sleep 3
    
    # 开始监控
    idf.py -p "$SERIAL_PORT" monitor
}

# 运行测试
run_test() {
    print_info "准备运行唤醒响应测试..."
    
    # 检查测试脚本
    if [ ! -f "scripts/wakeup_test.py" ]; then
        print_error "测试脚本未找到: scripts/wakeup_test.py"
        return 1
    fi
    
    # 安装Python依赖
    print_info "安装Python依赖..."
    pip3 install pyserial 2>/dev/null || print_warning "pyserial安装失败，请手动安装"
    
    # 运行测试
    print_info "运行唤醒响应测试..."
    python3 scripts/wakeup_test.py
}

# 显示使用说明
show_usage() {
    echo "小智唤醒响应优化部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项] [串口设备]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -b, --build    仅编译，不烧录"
    echo "  -f, --flash    编译并烧录"
    echo "  -m, --monitor  烧录后监控串口输出"
    echo "  -t, --test     运行唤醒响应测试"
    echo "  -a, --all      执行完整流程(编译+烧录+监控)"
    echo ""
    echo "示例:"
    echo "  $0 --build                    # 仅编译"
    echo "  $0 --flash /dev/ttyUSB0       # 编译并烧录到指定串口"
    echo "  $0 --all /dev/ttyUSB0         # 完整流程"
    echo "  $0 --test                     # 运行测试"
}

# 主函数
main() {
    echo "🎯 小智唤醒响应优化部署工具"
    echo "=================================="
    
    # 解析命令行参数
    case "$1" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -b|--build)
            check_dependencies
            show_optimizations
            backup_files
            build_project
            print_success "编译完成!"
            ;;
        -f|--flash)
            check_dependencies
            show_optimizations
            backup_files
            build_project
            flash_firmware "$2"
            print_success "烧录完成!"
            ;;
        -m|--monitor)
            check_dependencies
            show_optimizations
            backup_files
            build_project
            flash_firmware "$2"
            monitor_serial
            ;;
        -t|--test)
            run_test
            ;;
        -a|--all)
            check_dependencies
            show_optimizations
            backup_files
            build_project
            flash_firmware "$2"
            print_success "部署完成! 设备正在重启..."
            sleep 5
            print_info "现在可以测试唤醒功能了"
            print_info "说'你好小智'来测试唤醒响应"
            
            # 询问是否运行自动测试
            read -p "是否运行自动测试? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                run_test
            fi
            ;;
        *)
            print_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
