#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小智唤醒响应优化测试脚本
用于测试和验证唤醒响应优化效果
"""

import time
import serial
import re
import statistics
from datetime import datetime

class WakeupTester:
    def __init__(self, serial_port='/dev/ttyUSB0', baudrate=115200):
        """初始化测试器"""
        self.serial_port = serial_port
        self.baudrate = baudrate
        self.ser = None
        self.wakeup_stats = {
            'total_attempts': 0,
            'successful_wakeups': 0,
            'response_times': [],
            'success_rate': 0.0
        }
        
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.serial_port, self.baudrate, timeout=1)
            print(f"✅ 已连接到串口: {self.serial_port}")
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("🔌 串口已断开")
    
    def read_serial_data(self, timeout=5):
        """读取串口数据"""
        if not self.ser or not self.ser.is_open:
            return None
            
        start_time = time.time()
        data_buffer = ""
        
        while time.time() - start_time < timeout:
            if self.ser.in_waiting > 0:
                try:
                    data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                    data_buffer += data
                    
                    # 检查是否包含唤醒成功信息
                    if "WAKEWORD DETECTED" in data_buffer:
                        return data_buffer
                        
                except Exception as e:
                    print(f"⚠️ 读取串口数据错误: {e}")
            
            time.sleep(0.1)
        
        return data_buffer if data_buffer else None
    
    def parse_wakeup_stats(self, data):
        """解析唤醒统计信息"""
        if not data:
            return None
            
        # 查找唤醒统计信息
        stats_pattern = r"Wakeup Stats - Success: (\d+), Attempts: (\d+), Rate: ([\d.]+)%"
        match = re.search(stats_pattern, data)
        
        if match:
            return {
                'success': int(match.group(1)),
                'attempts': int(match.group(2)),
                'rate': float(match.group(3))
            }
        
        return None
    
    def test_wakeup_response(self, test_duration=60):
        """测试唤醒响应"""
        print(f"🎯 开始唤醒响应测试，持续时间: {test_duration}秒")
        print("📋 测试说明:")
        print("   1. 请在测试期间多次说'你好小智'")
        print("   2. 尝试不同的距离和角度")
        print("   3. 脚本会自动记录唤醒统计信息")
        print("=" * 50)
        
        start_time = time.time()
        last_stats = None
        
        while time.time() - start_time < test_duration:
            # 读取串口数据
            data = self.read_serial_data(timeout=2)
            
            if data:
                # 检查唤醒成功
                if "WAKEWORD DETECTED" in data:
                    self.wakeup_stats['successful_wakeups'] += 1
                    print(f"✅ 检测到唤醒成功! 总成功次数: {self.wakeup_stats['successful_wakeups']}")
                
                # 解析统计信息
                stats = self.parse_wakeup_stats(data)
                if stats and stats != last_stats:
                    last_stats = stats
                    print(f"📊 当前统计: 成功{stats['success']}次, 尝试{stats['attempts']}次, 成功率{stats['rate']:.1f}%")
            
            # 显示进度
            elapsed = time.time() - start_time
            remaining = test_duration - elapsed
            if int(elapsed) % 10 == 0:  # 每10秒显示一次进度
                print(f"⏱️  测试进行中... 剩余时间: {remaining:.0f}秒")
        
        print("🏁 测试完成!")
        return last_stats
    
    def analyze_mic_volume(self):
        """分析麦克风音量设置"""
        print("🎤 分析麦克风音量设置...")
        
        # 发送命令获取当前配置
        data = self.read_serial_data(timeout=3)
        
        if data:
            # 查找麦克风音量信息
            mic_vol_pattern = r"micInitVol: (\d+)"
            match = re.search(mic_vol_pattern, data)
            
            if match:
                mic_vol = int(match.group(1))
                print(f"📊 当前麦克风音量: {mic_vol}")
                
                if mic_vol < 8:
                    print("⚠️  建议: 麦克风音量偏低，建议调整到8-10")
                elif mic_vol > 12:
                    print("⚠️  建议: 麦克风音量偏高，可能导致噪声")
                else:
                    print("✅ 麦克风音量设置合理")
                
                return mic_vol
        
        print("❌ 无法获取麦克风音量信息")
        return None
    
    def generate_report(self, stats):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 小智唤醒响应优化测试报告")
        print("=" * 60)
        print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if stats:
            print(f"📊 测试结果:")
            print(f"   ✅ 成功唤醒次数: {stats['success']}")
            print(f"   🎯 总尝试次数: {stats['attempts']}")
            print(f"   📈 成功率: {stats['rate']:.1f}%")
            
            # 评估结果
            if stats['rate'] >= 85:
                print("🎉 评估: 优秀! 唤醒响应非常好")
            elif stats['rate'] >= 70:
                print("👍 评估: 良好! 唤醒响应较好")
            elif stats['rate'] >= 50:
                print("⚠️  评估: 一般，需要进一步优化")
            else:
                print("❌ 评估: 较差，需要检查配置和环境")
        else:
            print("❌ 未能获取有效的测试统计数据")
        
        print("\n💡 优化建议:")
        print("   1. 如果成功率低于70%，尝试调整麦克风音量到8-10")
        print("   2. 确保在相对安静的环境中测试")
        print("   3. 保持1-2米的合适距离")
        print("   4. 尽量面向设备说话")
        print("=" * 60)

def main():
    """主函数"""
    print("🎯 小智唤醒响应优化测试工具")
    print("=" * 40)
    
    # 创建测试器
    tester = WakeupTester()
    
    try:
        # 连接设备
        if not tester.connect():
            return
        
        # 分析当前配置
        tester.analyze_mic_volume()
        
        # 等待用户准备
        input("\n按回车键开始测试...")
        
        # 执行测试
        stats = tester.test_wakeup_response(test_duration=60)
        
        # 生成报告
        tester.generate_report(stats)
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        # 断开连接
        tester.disconnect()

if __name__ == "__main__":
    main()
