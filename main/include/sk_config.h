/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_config.h
 * @description: 配置模块接口
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_CONFIG_H
#define SK_CONFIG_H
#include <stdint.h>
#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    char ssid[SSID_MAX_LEN + 1];
    char password[PASSWORD_MAX_LEN + 1];
} SkSsidItem;

typedef struct {
    uint8_t friendName[MAX_FRIEND_NAME + 1];
    uint8_t friendId[MAX_FRIEND_ID + 1];
} SkFriendInfo;

void SkConfigInit();
void SkConfigStartServer();
void SkConfigStopServer();
int32_t SkConfigGetFriends(SkFriendInfo *friends, uint32_t maxCnt);
int32_t SkConfigGetSelfTermId(uint8_t *termId, uint32_t maxLen);
SkSsidItem* SkConfigGetSsid(char *ssid);
void SkConfigGetServerIp(char *ip, uint32_t maxLen);
char* SkConfigGetCtrlServer();
void SkConfigGetCalleeList(uint8_t *calleeList, uint32_t bufferSize);
void SkConfigSetCalleeList(uint8_t *calleeList, uint32_t bufferSize);
void SkConfigSetCtrlServer(char *server);
void SkConfigSetCtrlServerPort(uint16_t port);
void SkConfigSetSpkInitVol(uint8_t vol);
void SkConfigSetMicInitVol(uint8_t vol);
// 🔥 优化：添加新的麦克风音量调整接口
void SkConfigSetMicVolume(uint8_t volume);
uint8_t SkConfigGetMicVolume();
void SkConfigSave();
int32_t SkConfigAddSsid(int8_t *ssid, int8_t *password);
int32_t SkConfigDelSsid();
int32_t SkConfigGetSsidList(uint8_t *buffer, uint32_t len);

#ifdef __cplusplus
}
#endif

#endif