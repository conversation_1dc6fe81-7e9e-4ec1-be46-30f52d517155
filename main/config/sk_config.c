/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_config.c
 * @description: 配置参数的读写接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <freertos/FreeRTOS.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <cJSON.h>
#include "esp_err.h"
#include "sk_log.h"
#include "sk_board.h"
#include "sk_os.h"
#include "sk_config.h"

static const char *TAG = "SkConfig";

#define NVS_NAMESPACE "SkConfig"

typedef struct {
    char ctrlServer[64];
    uint8_t spkInitVol;
    uint8_t micInitVol;
    uint16_t ctrlServerPort;
    uint8_t chipId[16];
    uint8_t ssidCnt;
    SkSsidItem ssidList[SSID_MAX_CNT];
    uint8_t *calleeNameList;
    uint16_t calleeNameLen;
} SkConfigCtrl;

SkConfigCtrl g_skConfig;

void SaveToNvs(SkConfigCtrl *ctrl);
void LoadFromNvs(SkConfigCtrl *ctrl);


int32_t SkConfigGetSsidList(uint8_t *buffer, uint32_t len) {
    SkConfigCtrl *ctrl = &g_skConfig;
    int32_t ret = SK_RET_INVALID_PARAM;

    if (buffer == NULL) {
        return ret;
    }

    cJSON *root = cJSON_CreateObject();
    cJSON *ssidList = cJSON_CreateArray();
    cJSON_AddStringToObject(root, "operation", "get_ssid");
    cJSON_AddItemToObject(root, "ssid_list", ssidList);
    for (int i = 0; i < ctrl->ssidCnt; i++) {
        cJSON_AddItemToArray(ssidList, cJSON_CreateString(ctrl->ssidList[i].ssid));
    }

    char *json = cJSON_PrintUnformatted(root);
    if (json != NULL) {
        if (strlen(json) <= len) {
            ret = SK_RET_SUCCESS;
            memcpy(buffer, json, len);
            buffer[strlen(json)] = '\0';
        } else {
            ret = SK_RET_INVALID_PARAM;
            SK_LOGI(TAG, "json len: %d, buffer len: %d", strlen(json), len);
        }
        cJSON_free(json);
    }
    cJSON_Delete(root);

    return ret;
}

int32_t SkConfigAddSsid(int8_t *ssid, int8_t *password) {
    SkConfigCtrl *ctrl = &g_skConfig;
    uint32_t index = ctrl->ssidCnt;

    for (int i = 0; i < ctrl->ssidCnt; i++) {
        if (strcmp(ctrl->ssidList[i].ssid, (char *)ssid) == 0) {
            index = i;
            break;
        }
    }
    
    SK_LOGI(TAG, "ssid: %s, cnt: %d", ssid, index);
    if (index < SSID_MAX_CNT) {
        strcpy(ctrl->ssidList[index].ssid, (char *)ssid);
        if (password != NULL) {
            strcpy(ctrl->ssidList[index].password, (char *)password);
        } else {
            memset(ctrl->ssidList[index].password, 0, 
                sizeof(ctrl->ssidList[index].password));
        }
        if (index == ctrl->ssidCnt) {
            ctrl->ssidCnt++;
        }
        SaveToNvs(ctrl);
        LoadFromNvs(ctrl);
    }

    return ESP_OK;
}

int32_t SkConfigDelSsid() {
    SkConfigCtrl *ctrl = &g_skConfig;

    for (int i = 0; i < ctrl->ssidCnt; i++) {
        memset(ctrl->ssidList[i].ssid, 0, sizeof(ctrl->ssidList[i].ssid));
        memset(ctrl->ssidList[i].password, 0, sizeof(ctrl->ssidList[i].password));
    }
    ctrl->ssidCnt = 0;
    SaveToNvs(ctrl);
    LoadFromNvs(ctrl);
    return ESP_OK;
}

void LoadBasicInfo(SkConfigCtrl *ctrl, nvs_handle_t nvsHandle) {
    size_t length;

    length = sizeof(ctrl->ctrlServer);
    if (nvs_get_str(nvsHandle, "ctrlServer", ctrl->ctrlServer, &length) != ESP_OK) {
        strcpy(ctrl->ctrlServer, "192.168.3.15");
    }
    SK_LOGI(TAG, "ctrlServer: %s", ctrl->ctrlServer);

    if (nvs_get_u16(nvsHandle, "ctrlPort", &ctrl->ctrlServerPort) != ESP_OK) {
        ctrl->ctrlServerPort = 9527;
    }
    SK_LOGI(TAG, "ctrlServerPort: %d", ctrl->ctrlServerPort);

    if (nvs_get_u8(nvsHandle, "spkInitVol", &ctrl->spkInitVol) != ESP_OK) {
        ctrl->spkInitVol = 70;
    }
    SK_LOGI(TAG, "spkInitVol: %d", ctrl->spkInitVol);

    if (nvs_get_u8(nvsHandle, "micInitVol", &ctrl->micInitVol) != ESP_OK) {
        ctrl->micInitVol = 9;  // 🔥 优化：从6提升到9，提高语音信号强度
    }
    SK_LOGI(TAG, "micInitVol: %d (optimized for better wakeup response)", ctrl->micInitVol);

    length = 1024;
    if (nvs_get_blob(nvsHandle, "friendList", ctrl->calleeNameList, &length) != ESP_OK) {
        memset(ctrl->calleeNameList, 0, 1024);
    }
    ctrl->calleeNameLen = (length > 1024) ? 0: length;
    SK_LOGI(TAG, "calleeNameLen: %d", ctrl->calleeNameLen);

    return;
}

void LoadSsidList(SkConfigCtrl *ctrl, nvs_handle_t nvsHandle) {
    int32_t ssidCnt = 0;
    size_t length;
    char keySsid[12], keyPassword[12];
    char ssid[SSID_MAX_LEN + 1];
    char password[PASSWORD_MAX_LEN + 1];
    char *ptr = "鹏图教育";

    for (int i = 0; i < SSID_MAX_CNT; i++) {
        if (i > 0) {
            sprintf(keySsid, "ssid%d", i);
            sprintf(keyPassword, "password%d", i);
        } else {
            strcpy(keySsid, "ssid");
            strcpy(keyPassword, "password");
        }

        length = sizeof(ssid);
        if (nvs_get_str(nvsHandle, keySsid, ssid, &length) != ESP_OK) {
            continue;
        }
        length = sizeof(password);
        if (nvs_get_str(nvsHandle, keyPassword, password, &length) != ESP_OK) {
            continue;
        }
        SK_LOGI(TAG, "%d ssid: %s", ssidCnt, ssid);
        if (strcmp(ssid, ptr) == 0) {
            continue;
        }
        strcpy(ctrl->ssidList[ssidCnt].ssid, ssid);
        strcpy(ctrl->ssidList[ssidCnt].password, password);
        ssidCnt++;
    }
    if (ssidCnt == 0) {
        strcpy(ctrl->ssidList[0].ssid, "SK-Net");
        strcpy(ctrl->ssidList[0].password, "2025Succ");
        ssidCnt++;
    }

    ctrl->ssidCnt = ssidCnt;

    return;
}

void LoadFriendList(SkConfigCtrl *ctrl, nvs_handle_t nvsHandle) {
    size_t length;
    length = 1024;
    if (nvs_get_blob(nvsHandle, "friendList", ctrl->calleeNameList, &length) != ESP_OK) {
        SK_LOGE(TAG, "Failed to read friendList from NVS");
        return;
    }
    ctrl->calleeNameLen = (length > 1024) ? 0: length;

    return;
}

void LoadFromNvs(SkConfigCtrl *ctrl) {
    // Load ssid and password from NVS from namespace "wifi"
    // ssid, ssid1, ssid2, ... ssid9
    // password, password1, password2, ... password9
    nvs_handle_t nvsHandle;
    int32_t ret = nvs_open(NVS_NAMESPACE, NVS_READONLY, &nvsHandle);

    if (ret == ESP_ERR_NVS_NOT_FOUND) {
        SK_LOGW(TAG, "NVS namespace %s doesn't exist. Creating it...", NVS_NAMESPACE);
        // 打开默认命名空间并写入数据以创建命名空间
        ret = nvs_open("default", NVS_READWRITE, &nvsHandle);
        if (ret != ESP_OK) {
            SK_LOGE(TAG, "Failed to open default namespace: %s", esp_err_to_name(ret));
        }
        ret = nvs_set_str(nvsHandle, NVS_NAMESPACE, "dummy_value");
        if (ret != ESP_OK) {
            SK_LOGE(TAG, "Failed to set dummy value: %s", esp_err_to_name(ret));
            nvs_close(nvsHandle);
            return;
        }
        ret = nvs_commit(nvsHandle);
        if (ret == ESP_OK) {
            SK_LOGI(TAG, "Namespace %s created successfully.", NVS_NAMESPACE);
        } else {
            SK_LOGE(TAG, "Failed to commit changes: %s", esp_err_to_name(ret));
        }
        nvs_close(nvsHandle);
        ret = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvsHandle);
    }

    if (ret != ESP_OK) {
        // The namespace doesn't exist, just return
        SK_LOGW(TAG, "NVS namespace %s doesn't exist.", NVS_NAMESPACE);
        return;
    }

    LoadBasicInfo(ctrl, nvsHandle);
    LoadSsidList(ctrl, nvsHandle);
    LoadFriendList(ctrl, nvsHandle);
    nvs_close(nvsHandle);
}

void SaveBasicInfo(SkConfigCtrl *ctrl, nvs_handle_t nvsHandle) {
    nvs_set_str(nvsHandle, "ctrlServer", ctrl->ctrlServer);
    nvs_set_u16(nvsHandle, "ctrlPort", ctrl->ctrlServerPort);
    nvs_set_u8(nvsHandle, "spkInitVol", ctrl->spkInitVol);
    nvs_set_u8(nvsHandle, "micInitVol", ctrl->micInitVol);

    return;
}

/**
 * @brief 保存SSID列表
 *
 * 保存SSID列表到配置文件或数据库中。
 *
 * @param ctrl SkConfigCtrl对象指针，用于控制配置信息的保存
 */
void SaveSsidList(SkConfigCtrl *ctrl, nvs_handle_t nvsHandle) {
    char keySsid[12], keyPassword[12];
    
    for (int i = 0; i < SSID_MAX_CNT; i++) {
        if (i > 0) {
            sprintf(keySsid, "ssid%d", i);
            sprintf(keyPassword, "password%d", i);
        } else {
            strcpy(keySsid, "ssid");
            strcpy(keyPassword, "password");
        }
        SK_LOGI(TAG, "%d ssid: %s", i, 
            ctrl->ssidList[i].ssid);
        if (strlen(ctrl->ssidList[i].ssid) > 0) {
            nvs_set_str(nvsHandle, keySsid, ctrl->ssidList[i].ssid);
            nvs_set_str(nvsHandle, keyPassword, ctrl->ssidList[i].password);
        } else {
            nvs_erase_key(nvsHandle, keySsid);
            nvs_erase_key(nvsHandle, keyPassword);
        }
    }

    return;
}

void SaveFriendList(SkConfigCtrl *ctrl, nvs_handle_t nvsHandle) {
    nvs_set_blob(nvsHandle, "friendList", ctrl->calleeNameList, ctrl->calleeNameLen);
    return;
}

void SaveToNvs(SkConfigCtrl *ctrl) {
    nvs_handle_t nvsHandle;

    ESP_ERROR_CHECK(nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvsHandle));
    SaveBasicInfo(ctrl, nvsHandle);
    SaveSsidList(ctrl, nvsHandle);
    SaveFriendList(ctrl, nvsHandle);
    nvs_commit(nvsHandle);
    nvs_close(nvsHandle);

    return;
}

void SkConfigInit() {
    SkConfigCtrl *ctrl = &g_skConfig;
    uint8_t chipId[8];
    uint16_t chipId16; 

    ctrl->ssidCnt = 0;
    ctrl->calleeNameList = SkOsAllocPsram(1024, sizeof(uint32_t));
    ctrl->calleeNameLen = 0;

    memset(chipId, 0x00, sizeof(chipId));
    SkBspGetChipID(chipId, sizeof(chipId));
    chipId16 = (uint16_t)((uint16_t)chipId[4] << 8 | chipId[5]);
    sprintf((char *)ctrl->chipId, "T%05u", chipId16);
    SK_LOGI("Chip ID", "MAC: %02x%02x%02x%02x%02x%02x, chipId: %s", 
        chipId[0], chipId[1], chipId[2], chipId[3], chipId[4], chipId[5], 
        ctrl->chipId);
    LoadFromNvs(ctrl);

    return;
}

int32_t SkConfigGetSelfTermId(uint8_t *termId, uint32_t maxLen) {
    size_t len;
    len = strlen((char *)g_skConfig.chipId);
    len = len > maxLen ? maxLen : len;
    memcpy(termId, g_skConfig.chipId, len);
    return SK_RET_SUCCESS;
}

SkSsidItem* SkConfigGetSsid(char *ssid) {
    for (int i = 0; i < g_skConfig.ssidCnt; i++) {
        if (strcmp(g_skConfig.ssidList[i].ssid, ssid) == 0) {
            return &g_skConfig.ssidList[i];
        }
    }
    return NULL;
}

char* SkConfigGetCtrlServer() {
    return g_skConfig.ctrlServer;
}

void SkConfigGetCalleeList(uint8_t *calleeList, uint32_t bufferSize) {
    size_t len = g_skConfig.calleeNameLen;

    if (len > bufferSize) {
        len = bufferSize;
    }
    memcpy(calleeList, g_skConfig.calleeNameList, len);
    return;
}

void SkConfigSetCalleeList(uint8_t *calleeList, uint32_t bufferSize) {
    if (bufferSize >= 1024) {
        return;
    }
    memcpy(g_skConfig.calleeNameList, calleeList, bufferSize);
    g_skConfig.calleeNameLen = bufferSize;

    return;
}

void SkConfigSetCtrlServer(char *server) {
    strcpy(g_skConfig.ctrlServer, server);
    return;
}

void SkConfigSetCtrlServerPort(uint16_t port) {
    g_skConfig.ctrlServerPort = port;
    return;
}

void SkConfigSetSpkInitVol(uint8_t vol) {
    g_skConfig.spkInitVol = vol;
    return;
}

void SkConfigSetMicInitVol(uint8_t vol) {
    g_skConfig.micInitVol = vol;
    return;
}

void SkConfigSave() {
    SaveToNvs(&g_skConfig);
    return;
}

// 🔥 优化：添加运行时麦克风音量调整接口
void SkConfigSetMicVolume(uint8_t volume) {
    SkConfigCtrl *ctrl = &g_skConfig;

    if (volume > 15) {
        volume = 15; // 限制最大音量
    }

    ctrl->micInitVol = volume;
    SK_LOGI(TAG, "🎤 Mic volume adjusted to: %d", volume);

    // 立即应用到硬件
    SkBspSetMicVolume(volume);

    // 保存到NVS
    SaveToNvs(ctrl);
}

// 🔥 优化：获取当前麦克风音量
uint8_t SkConfigGetMicVolume() {
    return g_skConfig.micInitVol;
}
