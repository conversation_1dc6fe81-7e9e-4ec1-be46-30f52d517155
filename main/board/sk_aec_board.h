/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_aec_board.h
 * @description: 支持AEC的单板硬件定义.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_AEC_BOARD_H
#define SK_AEC_BOARD_H

#include "sdkconfig.h"
#include <driver/gpio.h>

#define AUDIO_INPUT_REFERENCE    true

#define CONFIG_7210_MIC1    1
#define CONFIG_7210_MIC2    2
#define CONFIG_7210_MIC3    3
#define CONFIG_7210_MIC4    4

#define CONFIG_SK_NORMAL_KEY    0
#define CONFIG_SK_ADC_KEY       1

#define CONFIG_SK_KEY_MODE_DEF          0
#define CONFIG_SK_KEY_MODE_PULL_UP_EN   1

#if (CONFIG_BOARD_TYPE == CONFIG_FT_BOARD_V_0_1)
#define BOARD_TYPE_NAME             "FT-Board"
#define AUDIO_I2S_PORT_CNT          2
#define CONFIG_I2C_MASTER_NUM       1
#define CONFIG_ADC_DEV_ENABLE       1
#define CONFIG_RMT_LED_ENABLE       1
#define RMT_LED_STRIP_GPIO_NUM      GPIO_NUM_13
#define CONFIG_BOARD_MIC            CONFIG_7210_MIC1
#define CONFIG_REF_MIC              CONFIG_7210_MIC2
#define CONFIG_FUNC_KEY_MODE        CONFIG_SK_NORMAL_KEY
#define CONFIG_SYS_KEY_MODE         CONFIG_SK_KEY_MODE_PULL_UP_EN

#define SPK_I2S_GPIO_MCLK           GPIO_NUM_7
#define SPK_I2S_GPIO_WS             GPIO_NUM_16             // LRCK
#define SPK_I2S_GPIO_BCLK           GPIO_NUM_15
#define SPK_I2S_GPIO_DIN            GPIO_NUM_NC
#define SPK_I2S_GPIO_DOUT           GPIO_NUM_17

#define MIC_I2S_GPIO_MCLK           GPIO_NUM_12
#define MIC_I2S_GPIO_WS             GPIO_NUM_9              // LRCK
#define MIC_I2S_GPIO_BCLK           GPIO_NUM_10
#define MIC_I2S_GPIO_DIN            GPIO_NUM_3
#define MIC_I2S_GPIO_DOUT           GPIO_NUM_NC

#define AUDIO_CODEC_PA_PIN          GPIO_NUM_46
#define AUDIO_CODEC_I2C_SDA_PIN     GPIO_NUM_8
#define AUDIO_CODEC_I2C_SCL_PIN     GPIO_NUM_18
#define AUDIO_CODEC_ES8311_ADDR     ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR     ES7210_CODEC_DEFAULT_ADDR

#define VOLUME_UP_BUTTON_GPIO       GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO     GPIO_NUM_NC
#define GPIO_FUNC_KEY1              (GPIO_NUM_0)
#define GPIO_FUNC_KEY2              (GPIO_NUM_11)
#define GPIO_FUNC_KEY3              (GPIO_NUM_21)
#define GPIO_KEY_INPUT              (GPIO_NUM_14)

#define	ICM42607_ADDR	            0x68
#define CONFIG_SK_THS_DEV_ENABLE    1
#define CONFIG_SK_GYRO_DEV_ENABLE   1

#define BAT_ADC_UNIT                ADC_UNIT_1
#define BAT_ADC_CHAN                ADC_CHANNEL_5   //GPIO6 ADC1 Channel5

#elif (CONFIG_BOARD_TYPE == CONFIG_SK_BOARD_BOX3)
#define BOARD_TYPE_NAME             "BOX3-Board"
#define AUDIO_I2S_PORT_CNT          1
#define CONFIG_I2C_MASTER_NUM       1
#define CONFIG_BOARD_MIC            CONFIG_7210_MIC1
#define CONFIG_REF_MIC              CONFIG_7210_MIC3
#define CONFIG_FUNC_KEY_MODE        CONFIG_SK_NORMAL_KEY
#define CONFIG_SYS_KEY_MODE         CONFIG_SK_KEY_MODE_PULL_UP_EN

#define SPK_I2S_GPIO_MCLK           GPIO_NUM_2
#define SPK_I2S_GPIO_WS             GPIO_NUM_45             // LRCK
#define SPK_I2S_GPIO_BCLK           GPIO_NUM_17
#define SPK_I2S_GPIO_DIN            GPIO_NUM_16
#define SPK_I2S_GPIO_DOUT           GPIO_NUM_15

#define MIC_I2S_GPIO_MCLK           GPIO_NUM_2
#define MIC_I2S_GPIO_WS             GPIO_NUM_45              // LRCK
#define MIC_I2S_GPIO_BCLK           GPIO_NUM_17
#define MIC_I2S_GPIO_DIN            GPIO_NUM_16
#define MIC_I2S_GPIO_DOUT           GPIO_NUM_15

#define AUDIO_CODEC_PA_PIN          GPIO_NUM_46
#define AUDIO_CODEC_I2C_SDA_PIN     GPIO_NUM_8
#define AUDIO_CODEC_I2C_SCL_PIN     GPIO_NUM_18
#define AUDIO_CODEC_ES8311_ADDR     ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR     ES7210_CODEC_DEFAULT_ADDR

#define BOOT_BUTTON_GPIO            GPIO_NUM_0
#define VOLUME_UP_BUTTON_GPIO       GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO     GPIO_NUM_NC
#define GPIO_FUNC_KEY1              (GPIO_NUM_9)
#define GPIO_FUNC_KEY2              (GPIO_NUM_10)
#define GPIO_FUNC_KEY3              (GPIO_NUM_11)
#define GPIO_KEY_INPUT              (GPIO_NUM_12)

#define	ICM42607_ADDR	            0x68
#define CONFIG_SK_THS_DEV_ENABLE    0
#define CONFIG_SK_GYRO_DEV_ENABLE   1

#elif (CONFIG_BOARD_TYPE == CONFIG_SK_BOARD_V_0_1)
#define BOARD_TYPE_NAME             "Outsource-Board"
#define CONFIG_BOARD_MIC            CONFIG_7210_MIC1
#define CONFIG_REF_MIC              CONFIG_7210_MIC3
#define CONFIG_ADC_DEV_ENABLE       1
#define CONFIG_RMT_LED_ENABLE       1
#define RMT_LED_STRIP_GPIO_NUM      21
#define CONFIG_HEADPHONE_ENABLE     1
#define CONFIG_VIBRATION_ENABLE     1

#define AUDIO_I2S_PORT_CNT          2
#define CONFIG_I2C_MASTER_NUM       2
#define CONFIG_FUNC_KEY_MODE        CONFIG_SK_ADC_KEY
#define CONFIG_SYS_KEY_MODE         CONFIG_SK_KEY_MODE_DEF

#define SHAKE_SENSOR_GPIO           GPIO_NUM_2

#define AUDIO_CODEC_I2C_SCL_PIN     GPIO_NUM_4
#define AUDIO_CODEC_I2C_SDA_PIN     GPIO_NUM_5

#define SPK_I2S_GPIO_MCLK           GPIO_NUM_42             // 
#define SPK_I2S_GPIO_WS             GPIO_NUM_41             // LRCK 16
#define SPK_I2S_GPIO_BCLK           GPIO_NUM_40             // 512
#define SPK_I2S_GPIO_DIN            GPIO_NUM_38
#define SPK_I2S_GPIO_DOUT           GPIO_NUM_39

#define MIC_I2S_GPIO_MCLK           GPIO_NUM_20
#define MIC_I2S_GPIO_WS             GPIO_NUM_9              // LRCK
#define MIC_I2S_GPIO_BCLK           GPIO_NUM_10
#define MIC_I2S_GPIO_DIN            GPIO_NUM_11
#define MIC_I2S_GPIO_DOUT           GPIO_NUM_NC

#define AUDIO_CODEC_PA_PIN          GPIO_NUM_14
#define SENSOR_I2C_SCL_PIN          GPIO_NUM_15
#define SENSOR_I2C_SDA_PIN          GPIO_NUM_16
#define KEY_ADC_CHAN                ADC_CHANNEL_6   //GPIO17 ADC2 Channel6
#define BAT_ADC_CHAN                ADC_CHANNEL_7   //GPIO18 ADC2 Channel7
#define GPIO_KEY_INPUT              (GPIO_NUM_19)
#define GPIO_PHONE_INT              GPIO_NUM_13

#define AUDIO_CODEC_ES8311_ADDR     ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR     ES7210_CODEC_DEFAULT_ADDR
#define	ICM42607_ADDR	            0x69
#define CONFIG_SK_THS_DEV_ENABLE    1
#define CONFIG_SK_GYRO_DEV_ENABLE   1

#endif

#define I2C_MASTER_FREQ_HZ          100000
#define AHT30_ADDR                  0x38            // AHT30 I2C Address

#endif // SK_AEC_BOARD_H
